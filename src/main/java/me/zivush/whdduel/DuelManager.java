package me.zivush.whdduel;

import net.md_5.bungee.api.chat.ClickEvent;
import net.md_5.bungee.api.chat.ComponentBuilder;
import net.md_5.bungee.api.chat.HoverEvent;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class DuelManager {
    private final WHDDuel plugin;
    private final ConfigManager configManager;
    
    // Active duels: player UUID -> duel data
    private final Map<UUID, DuelData> activeDuels = new ConcurrentHashMap<>();
    
    // Pending requests: target UUID -> request data
    private final Map<UUID, DuelRequest> pendingRequests = new ConcurrentHashMap<>();
    
    // Players currently in duel setup GUI
    private final Set<UUID> playersInSetup = ConcurrentHashMap.newKeySet();

    // Map to store target players for GUI sessions
    private final Map<UUID, UUID> guiTargets = new ConcurrentHashMap<>();
    
    public DuelManager(WHDDuel plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
    }
    
    public void openDuelSetupGUI(Player player, Player target) {
        if (isPlayerInDuel(player)) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("already_in_duel")));
            return;
        }
        
        if (isPlayerInDuel(target)) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("target_in_duel", "target", target.getName())));
            return;
        }
        
        playersInSetup.add(player.getUniqueId());
        guiTargets.put(player.getUniqueId(), target.getUniqueId());

        Inventory gui = createSetupGUI(player, target);
        player.openInventory(gui);
    }
    
    private Inventory createSetupGUI(Player player, Player target) {
        String title = ChatColor.translateAlternateColorCodes('&', configManager.getGuiTitle());
        int size = configManager.getGuiSize();
        Inventory gui = Bukkit.createInventory(null, size, title);
        
        // Fill with filler items
        ItemStack filler = new ItemStack(configManager.getFillerMaterial());
        ItemMeta fillerMeta = filler.getItemMeta();
        if (fillerMeta != null) {
            fillerMeta.setDisplayName(ChatColor.translateAlternateColorCodes('&', 
                configManager.getFillerDisplayName()));
            filler.setItemMeta(fillerMeta);
        }
        
        for (int i = 0; i < size; i++) {
            gui.setItem(i, filler);
        }
        
        // Add restriction options
        for (String option : configManager.getRestrictedOptions()) {
            int slot = configManager.getOptionSlot(option);
            if (slot >= 0 && slot < size) {
                ItemStack optionItem = createOptionItem(option, true); // Default to enabled
                gui.setItem(slot, optionItem);
            }
        }
        
        // Add done button
        int doneSlot = configManager.getDoneButtonSlot();
        if (doneSlot >= 0 && doneSlot < size) {
            ItemStack doneButton = createDoneButton(target);
            gui.setItem(doneSlot, doneButton);
        }
        
        // Add cancel button
        int cancelSlot = configManager.getCancelButtonSlot();
        if (cancelSlot >= 0 && cancelSlot < size) {
            ItemStack cancelButton = createCancelButton();
            gui.setItem(cancelSlot, cancelButton);
        }
        
        return gui;
    }
    
    private ItemStack createOptionItem(String option, boolean enabled) {
        Material material = configManager.getOptionMaterial(option);
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            String displayName = ChatColor.translateAlternateColorCodes('&', 
                configManager.getOptionDisplayName(option));
            meta.setDisplayName(displayName);
            
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.translateAlternateColorCodes('&', 
                configManager.getOptionDescription(option)));
            lore.add("");
            
            if (enabled) {
                lore.add(ChatColor.translateAlternateColorCodes('&', 
                    configManager.getEnabledItemDisplayName()));
                configManager.getEnabledItemLore().forEach(line ->
                    lore.add(ChatColor.translateAlternateColorCodes('&', line)));
            } else {
                lore.add(ChatColor.translateAlternateColorCodes('&', 
                    configManager.getDisabledItemDisplayName()));
                configManager.getDisabledItemLore().forEach(line ->
                    lore.add(ChatColor.translateAlternateColorCodes('&', line)));
            }
            
            meta.setLore(lore);
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    private ItemStack createDoneButton(Player target) {
        ItemStack button = new ItemStack(configManager.getDoneButtonMaterial());
        ItemMeta meta = button.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', 
                configManager.getDoneButtonDisplayName()));
            
            List<String> lore = new ArrayList<>();
            configManager.getDoneButtonLore().forEach(line ->
                lore.add(ChatColor.translateAlternateColorCodes('&', line)));
            meta.setLore(lore);
            
            // Store target player name in NBT-like way (using lore)
            List<String> newLore = new ArrayList<>(lore);
            newLore.add("§0§0§0TARGET:" + target.getName()); // Hidden identifier
            meta.setLore(newLore);
            
            button.setItemMeta(meta);
        }
        
        return button;
    }
    
    private ItemStack createCancelButton() {
        ItemStack button = new ItemStack(configManager.getCancelButtonMaterial());
        ItemMeta meta = button.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', 
                configManager.getCancelButtonDisplayName()));
            
            List<String> lore = new ArrayList<>();
            configManager.getCancelButtonLore().forEach(line ->
                lore.add(ChatColor.translateAlternateColorCodes('&', line)));
            meta.setLore(lore);
            
            button.setItemMeta(meta);
        }
        
        return button;
    }
    
    public void handleGUIClick(Player player, int slot, ItemStack clickedItem) {
        // Debug logging
        if (configManager.isDebugEnabled()) {
            plugin.getLogger().info("handleGUIClick called for player: " + player.getName() + ", slot: " + slot);
        }

        if (!playersInSetup.contains(player.getUniqueId())) {
            if (configManager.isDebugEnabled()) {
                plugin.getLogger().info("Player " + player.getName() + " not in setup GUI");
            }
            return;
        }

        if (clickedItem == null || clickedItem.getType() == Material.AIR) {
            if (configManager.isDebugEnabled()) {
                plugin.getLogger().info("Clicked item is null or air");
            }
            return;
        }
        
        // Check if it's a restriction option
        for (String option : configManager.getRestrictedOptions()) {
            if (slot == configManager.getOptionSlot(option)) {
                toggleRestriction(player, option, slot);
                return;
            }
        }
        
        // Check if it's the done button
        if (slot == configManager.getDoneButtonSlot()) {
            if (configManager.isDebugEnabled()) {
                plugin.getLogger().info("Done button clicked at slot " + slot);
            }
            handleDoneButton(player, clickedItem);
            return;
        }
        
        // Check if it's the cancel button
        if (slot == configManager.getCancelButtonSlot()) {
            handleCancelButton(player);
            return;
        }
    }
    
    private void toggleRestriction(Player player, String option, int slot) {
        Inventory gui = player.getOpenInventory().getTopInventory();
        ItemStack currentItem = gui.getItem(slot);
        
        if (currentItem == null) return;
        
        // Determine current state by checking lore
        boolean currentlyEnabled = isRestrictionEnabled(currentItem);
        
        // Create new item with opposite state
        ItemStack newItem = createOptionItem(option, !currentlyEnabled);
        gui.setItem(slot, newItem);
    }
    
    private boolean isRestrictionEnabled(ItemStack item) {
        if (item.getItemMeta() == null || item.getItemMeta().getLore() == null) {
            return true;
        }
        
        List<String> lore = item.getItemMeta().getLore();
        String enabledText = ChatColor.translateAlternateColorCodes('&', 
            configManager.getEnabledItemDisplayName());
        
        return lore.stream().anyMatch(line -> line.equals(enabledText));
    }
    
    private void handleDoneButton(Player player, ItemStack doneButton) {
        // Debug logging
        if (configManager.isDebugEnabled()) {
            plugin.getLogger().info("handleDoneButton called for player: " + player.getName());
        }

        // Get target player from stored map (more reliable than lore)
        UUID targetUUID = guiTargets.get(player.getUniqueId());
        if (targetUUID == null) {
            // Fallback to lore method
            String targetName = extractTargetFromLore(doneButton);
            if (targetName != null) {
                Player fallbackTarget = Bukkit.getPlayer(targetName);
                if (fallbackTarget != null) {
                    targetUUID = fallbackTarget.getUniqueId();
                }
            }
        }

        if (targetUUID == null) {
            player.sendMessage(ChatColor.RED + "Error: Could not find target player!");
            player.closeInventory();
            removePlayerFromSetup(player);
            return;
        }

        Player target = Bukkit.getPlayer(targetUUID);
        if (target == null || !target.isOnline()) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("player_not_found", "player", "target player")));
            player.closeInventory();
            removePlayerFromSetup(player);
            return;
        }
        
        // Collect restrictions from GUI
        Set<String> disabledRestrictions = collectDisabledRestrictions(player);
        
        // Send duel request
        sendDuelRequest(player, target, disabledRestrictions, false);

        player.closeInventory();
        removePlayerFromSetup(player);
    }
    
    private void handleCancelButton(Player player) {
        player.closeInventory();
        removePlayerFromSetup(player);
    }
    
    private String extractTargetFromLore(ItemStack item) {
        if (item.getItemMeta() == null || item.getItemMeta().getLore() == null) {
            if (configManager.isDebugEnabled()) {
                plugin.getLogger().info("Item meta or lore is null");
            }
            return null;
        }

        if (configManager.isDebugEnabled()) {
            plugin.getLogger().info("Item lore: " + item.getItemMeta().getLore());
        }

        for (String line : item.getItemMeta().getLore()) {
            if (configManager.isDebugEnabled()) {
                plugin.getLogger().info("Checking lore line: '" + line + "'");
            }
            if (line.startsWith("§0§0§0TARGET:")) {
                String targetName = line.substring("§0§0§0TARGET:".length());
                if (configManager.isDebugEnabled()) {
                    plugin.getLogger().info("Found target: " + targetName);
                }
                return targetName;
            }
        }

        if (configManager.isDebugEnabled()) {
            plugin.getLogger().info("No target found in lore");
        }
        return null;
    }
    
    private Set<String> collectDisabledRestrictions(Player player) {
        Set<String> disabled = new HashSet<>();
        Inventory gui = player.getOpenInventory().getTopInventory();
        
        for (String option : configManager.getRestrictedOptions()) {
            int slot = configManager.getOptionSlot(option);
            ItemStack item = gui.getItem(slot);
            
            if (item != null && !isRestrictionEnabled(item)) {
                disabled.add(option);
            }
        }
        
        return disabled;
    }
    
    public boolean isPlayerInDuel(Player player) {
        return activeDuels.containsKey(player.getUniqueId());
    }
    
    public boolean isPlayerInSetup(Player player) {
        return playersInSetup.contains(player.getUniqueId());
    }
    
    public void removePlayerFromSetup(Player player) {
        playersInSetup.remove(player.getUniqueId());
        guiTargets.remove(player.getUniqueId());
    }

    public Player getOpponent(Player player) {
        DuelData data = activeDuels.get(player.getUniqueId());
        if (data == null) {
            return null;
        }
        return Bukkit.getPlayer(data.getOpponentUUID());
    }

    public void sendInstantDuelRequest(Player sender, Player target) {
        // Check if players are already in duels (additional safety check)
        if (isPlayerInDuel(sender)) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("already_in_duel")));
            return;
        }

        if (isPlayerInDuel(target)) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("target_in_duel", "target", target.getName())));
            return;
        }

        // Send duel request with default values (empty set = all restrictions enabled/allowed)
        Set<String> defaultRestrictions = new HashSet<>();
        sendDuelRequest(sender, target, defaultRestrictions, true);
    }
    
    private void sendDuelRequest(Player sender, Player target, Set<String> disabledRestrictions, boolean isInstant) {
        // Create duel request
        DuelRequest request = new DuelRequest(sender.getUniqueId(), target.getUniqueId(), disabledRestrictions);
        pendingRequests.put(target.getUniqueId(), request);

        // Send messages based on duel type
        if (isInstant) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("duel_request_sent_instant", "target", target.getName())));
            target.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("duel_request_received_instant", "sender", sender.getName())));
        } else {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("duel_request_sent", "target", target.getName())));
            target.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("duel_request_received", "sender", sender.getName())));
        }

        // Show restrictions
        if (!disabledRestrictions.isEmpty()) {
            String restrictionsText = String.join(", ", disabledRestrictions);
            target.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("duel_request_restrictions", "restrictions", restrictionsText)));
        }

        // Send accept/deny buttons (clickable text)
        sendClickableAcceptDeny(target, sender.getName());

        // Schedule request expiration
        new BukkitRunnable() {
            @Override
            public void run() {
                if (pendingRequests.remove(target.getUniqueId()) != null) {
                    target.sendMessage(ChatColor.translateAlternateColorCodes('&',
                        configManager.getMessage("duel_request_expired")));
                    sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                        configManager.getMessage("duel_request_expired")));
                }
            }
        }.runTaskLater(plugin, configManager.getRequestExpireSeconds() * 20L);
    }

    private void sendClickableAcceptDeny(Player target, String senderName) {
        // Create accept button
        TextComponent acceptButton = new TextComponent(ChatColor.translateAlternateColorCodes('&',
            configManager.getMessage("duel_request_accept")));
        acceptButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/duelaccept"));
        acceptButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT,
            new ComponentBuilder("Click to accept the duel from " + senderName).create()));

        // Create deny button
        TextComponent denyButton = new TextComponent(ChatColor.translateAlternateColorCodes('&',
            configManager.getMessage("duel_request_deny")));
        denyButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/dueldeny"));
        denyButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT,
            new ComponentBuilder("Click to deny the duel from " + senderName).create()));

        // Send the clickable components
        target.spigot().sendMessage(acceptButton);
        target.spigot().sendMessage(denyButton);
    }

    public void acceptDuelRequest(Player player) {
        DuelRequest request = pendingRequests.remove(player.getUniqueId());
        if (request == null) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("no_pending_request")));
            return;
        }

        Player sender = Bukkit.getPlayer(request.getSenderUUID());
        if (sender == null || !sender.isOnline()) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("player_not_found", "player", "sender")));
            return;
        }

        // Check if either player is already in a duel
        if (isPlayerInDuel(player) || isPlayerInDuel(sender)) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("already_in_duel")));
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("target_in_duel", "target", player.getName())));
            return;
        }

        startDuel(sender, player, request.getDisabledRestrictions());
    }

    public void denyDuelRequest(Player player) {
        DuelRequest request = pendingRequests.remove(player.getUniqueId());
        if (request == null) {
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("no_pending_request")));
            return;
        }

        Player sender = Bukkit.getPlayer(request.getSenderUUID());
        if (sender != null && sender.isOnline()) {
            sender.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("duel_request_denied", "target", player.getName())));
        }
    }

    private void startDuel(Player player1, Player player2, Set<String> disabledRestrictions) {
        // Check arena positions
        Location pos1 = configManager.getArenaPosition(1);
        Location pos2 = configManager.getArenaPosition(2);

        if (pos1 == null || pos2 == null) {
            player1.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("position_not_set")));
            player2.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("position_not_set")));
            return;
        }

        // Store original locations only (no inventory management)
        DuelData duel1 = new DuelData(player2.getUniqueId(), player1.getLocation(), disabledRestrictions);
        DuelData duel2 = new DuelData(player1.getUniqueId(), player2.getLocation(), disabledRestrictions);

        activeDuels.put(player1.getUniqueId(), duel1);
        activeDuels.put(player2.getUniqueId(), duel2);

        // Teleport players
        player1.teleport(pos1);
        player2.teleport(pos2);

        // Send start messages
        player1.sendMessage(ChatColor.translateAlternateColorCodes('&',
            configManager.getMessage("duel_started")));
        player2.sendMessage(ChatColor.translateAlternateColorCodes('&',
            configManager.getMessage("duel_started")));

        if (configManager.shouldLogDuelEvents()) {
            plugin.getLogger().info("Duel started between " + player1.getName() + " and " + player2.getName());
        }
    }

    public void endDuel(Player winner, Player loser, boolean disconnect) {
        DuelData winnerData = activeDuels.remove(winner.getUniqueId());
        DuelData loserData = activeDuels.remove(loser.getUniqueId());

        if (winnerData == null || loserData == null) {
            return; // Not in a duel
        }

        // Only teleport players back to original locations (no inventory restoration)
        winner.teleport(winnerData.getOriginalLocation());
        if (!disconnect) {
            loser.teleport(loserData.getOriginalLocation());
        }

        // Send end messages
        winner.sendMessage(ChatColor.translateAlternateColorCodes('&',
            configManager.getMessage("duel_ended_winner")));

        if (!disconnect) {
            loser.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("duel_ended_loser")));
        } else {
            winner.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("duel_ended_disconnect")));
        }

        if (configManager.shouldLogDuelEvents()) {
            plugin.getLogger().info("Duel ended: " + winner.getName() + " defeated " + loser.getName());
        }
    }





    public boolean isRestricted(Player player, Material material) {
        DuelData data = activeDuels.get(player.getUniqueId());
        if (data == null) {
            return false; // Not in duel
        }

        for (String restriction : data.getDisabledRestrictions()) {
            List<String> restrictedItems = configManager.getRestrictedItems(restriction.toLowerCase());
            if (restrictedItems.contains(material.name())) {
                return true;
            }
        }

        return false;
    }

    public void handlePlayerDisconnect(Player player) {
        DuelData data = activeDuels.get(player.getUniqueId());
        if (data != null) {
            Player opponent = Bukkit.getPlayer(data.getOpponentUUID());
            if (opponent != null && opponent.isOnline()) {
                endDuel(opponent, player, true);
            } else {
                activeDuels.remove(player.getUniqueId());
            }
        }

        // Remove from pending requests
        pendingRequests.remove(player.getUniqueId());
        playersInSetup.remove(player.getUniqueId());
        guiTargets.remove(player.getUniqueId());
    }

    // Inner classes for data storage
    public static class DuelRequest {
        private final UUID senderUUID;
        private final UUID targetUUID;
        private final Set<String> disabledRestrictions;

        public DuelRequest(UUID senderUUID, UUID targetUUID, Set<String> disabledRestrictions) {
            this.senderUUID = senderUUID;
            this.targetUUID = targetUUID;
            this.disabledRestrictions = disabledRestrictions;
        }

        public UUID getSenderUUID() { return senderUUID; }
        public UUID getTargetUUID() { return targetUUID; }
        public Set<String> getDisabledRestrictions() { return disabledRestrictions; }
    }

    public static class DuelData {
        private final UUID opponentUUID;
        private final Location originalLocation;
        private final Set<String> disabledRestrictions;

        public DuelData(UUID opponentUUID, Location originalLocation, Set<String> disabledRestrictions) {
            this.opponentUUID = opponentUUID;
            this.originalLocation = originalLocation;
            this.disabledRestrictions = disabledRestrictions;
        }

        public UUID getOpponentUUID() { return opponentUUID; }
        public Location getOriginalLocation() { return originalLocation; }
        public Set<String> getDisabledRestrictions() { return disabledRestrictions; }
    }
}
