package me.zivush.whdduel;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityToggleGlideEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerItemConsumeEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import org.bukkit.inventory.ItemStack;

public class EventListener implements Listener {
    private final WHDDuel plugin;
    private final ConfigManager configManager;
    private final DuelManager duelManager;
    
    public EventListener(WHDDuel plugin, ConfigManager configManager, DuelManager duelManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.duelManager = duelManager;
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();

        if (!duelManager.isPlayerInDuel(player)) {
            return;
        }

        // Find opponent using the duel data
        Player opponent = duelManager.getOpponent(player);

        if (opponent != null) {
            // Let natural death mechanics handle inventory (items drop, experience lost, etc.)
            // No inventory manipulation - player loses items naturally

            // End the duel - both players just get teleported back
            duelManager.endDuel(opponent, player, false);
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        duelManager.handlePlayerDisconnect(player);
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        
        // Check if player is in duel setup GUI
        if (!duelManager.isPlayerInSetup(player)) {
            return;
        }
        
        // Check if it's the duel setup GUI
        String guiTitle = ChatColor.translateAlternateColorCodes('&', configManager.getGuiTitle());
        if (!event.getView().getTitle().equals(guiTitle)) {
            return;
        }
        
        // Cancel the event to prevent item movement
        event.setCancelled(true);
        
        // Handle the click
        duelManager.handleGUIClick(player, event.getSlot(), event.getCurrentItem());
    }
    
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        
        // Check if player was in duel setup GUI
        if (duelManager.isPlayerInSetup(player)) {
            String guiTitle = ChatColor.translateAlternateColorCodes('&', configManager.getGuiTitle());
            if (event.getView().getTitle().equals(guiTitle)) {
                // Remove player from setup when they close the GUI
                duelManager.removePlayerFromSetup(player);
            }
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        
        if (!duelManager.isPlayerInDuel(player)) {
            return;
        }
        
        ItemStack item = event.getItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        // Check if the item is restricted
        if (duelManager.isRestricted(player, item.getType())) {
            event.setCancelled(true);
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("restriction_blocked", "item", item.getType().name())));
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerItemConsume(PlayerItemConsumeEvent event) {
        Player player = event.getPlayer();
        
        if (!duelManager.isPlayerInDuel(player)) {
            return;
        }
        
        ItemStack item = event.getItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        // Check if the item is restricted
        if (duelManager.isRestricted(player, item.getType())) {
            event.setCancelled(true);
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', 
                configManager.getMessage("restriction_blocked", "item", item.getType().name())));
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityToggleGlide(EntityToggleGlideEvent event) {
        plugin.getLogger().info("EntityToggleGlideEvent " + event.isGliding());
        // Only handle players
        if (!(event.getEntity() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getEntity();

        if (!duelManager.isPlayerInDuel(player)) {
            return;
        }

        // Check if player is trying to start gliding and elytra is restricted
        if (event.isGliding() && duelManager.isRestricted(player, Material.ELYTRA)) {
            event.setCancelled(true);
            player.sendMessage(ChatColor.translateAlternateColorCodes('&',
                configManager.getMessage("restriction_blocked", "item", "ELYTRA")));
        }
    }
}
